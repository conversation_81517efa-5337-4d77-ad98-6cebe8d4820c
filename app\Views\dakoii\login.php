<?= $this->extend('templates/dakoii_public_template') ?>

<?= $this->section('content') ?>
<div class="login-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card card-dark login-card">
                    <div class="card-body p-5">
                        <!-- Logo and Title -->
                        <div class="text-center mb-4">
                            <img src="<?= base_url('assets/system_images/dakoii-systems-logo.png') ?>" alt="Dakoii Systems" style="height: 60px; filter: brightness(0) invert(1);" class="mb-3">
                            <h2 class="fw-bold text-primary-custom mb-2">Dakoii Panel</h2>
                            <p class="text-muted mb-0">Super Admin Interface</p>
                            <p class="text-muted small">System Management Portal</p>
                        </div>

                        <!-- Login Form -->
                        <form action="<?= base_url('dakoii/authenticate') ?>" method="post">
                            <?= csrf_field() ?>
                            
                            <!-- Username Field -->
                            <div class="mb-3">
                                <label for="username" class="form-label text-light">
                                    <i class="bi bi-person me-2"></i>Username
                                </label>
                                <input type="text" class="form-control form-control-lg" id="username" name="username" placeholder="Enter your username" required>
                            </div>

                            <!-- Password Field -->
                            <div class="mb-4">
                                <label for="password" class="form-label text-light">
                                    <i class="bi bi-lock me-2"></i>Password
                                </label>
                                <div class="position-relative">
                                    <input type="password" class="form-control form-control-lg" id="password" name="password" placeholder="Enter your password" required>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-muted" id="togglePassword" style="border: none; background: none; z-index: 10;">
                                        <i class="bi bi-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Remember Me -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label text-light" for="remember">
                                        Remember me
                                    </label>
                                </div>
                            </div>

                            <!-- Login Button -->
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary-custom btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Access Panel
                                </button>
                            </div>

                            <!-- Error Messages -->
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger" role="alert">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <?= session()->getFlashdata('error') ?>
                                </div>
                            <?php endif; ?>

                            <!-- Additional Links -->
                            <div class="text-center">
                                <p class="text-muted small mb-2">Need help accessing the panel?</p>
                                <a href="#support" class="text-primary-custom text-decoration-none small">
                                    <i class="bi bi-headset me-1"></i>Contact Support
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="text-center mt-4">
                    <div class="card card-dark">
                        <div class="card-body p-3">
                            <p class="text-warning small mb-2">
                                <i class="bi bi-shield-exclamation me-2"></i>
                                <strong>Security Notice</strong>
                            </p>
                            <p class="text-muted small mb-0">
                                This is a restricted area. All access attempts are logged and monitored. 
                                Unauthorized access is strictly prohibited.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('bi-eye');
            toggleIcon.classList.add('bi-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('bi-eye-slash');
            toggleIcon.classList.add('bi-eye');
        }
    });

    // Auto-focus on username field
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('username').focus();
    });
</script>
<?= $this->endSection() ?>
